{"permissions": {"allow": ["Bash(cd \"/mnt/d/personal/Desktop/hebing/qingqikaifaxxxxxxxxxxxxxxxxx/copybot-fix-price-debug-log/newcopy_v2\")", "<PERSON><PERSON>(find . -name \"*.toml\" -o -name \"*.json\" -o -name \"settings*\")", "Bash(redis-cli get filter_config)", "<PERSON><PERSON>(curl -s \"http://localhost:3000/api/wallets\" -H \"Authorization: Bearer admin:123456\")", "Bash(cargo check:*)", "Bash(docker build:*)", "WebSearch", "WebFetch(domain:docs.solana.com)", "WebFetch(domain:solana.com)", "WebFetch(domain:github.com)", "WebFetch(domain:docs.triton.one)"], "deny": [], "defaultMode": "acceptEdits"}}